# This GitHub Action updates the DIRECTORY.md file (if needed) when doing a git push or pull_request
name: Update Directory
permissions:
  contents: write
on:
  push:
    paths:
      - 'src/**'
  pull_request:
    paths:
      - 'src/**'
  workflow_dispatch:
    inputs:
      logLevel:
        description: 'Log level'
        required: true
        default: 'info'
        type: choice
        options:
          - info
          - warning
          - debug
jobs:
  update_directory_md:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@master
      - uses: actions/setup-python@v5
        with:
          python-version: '3.x'
      - name: Update Directory
        shell: python
        run: |
            import os
            from typing import Iterator

            URL_BASE = "https://github.com/TheAlgorithms/Java/blob/master"
            g_output = []


            def good_filepaths(top_dir: str = ".") -> Iterator[str]:
                for dirpath, dirnames, filenames in os.walk(top_dir):
                    dirnames[:] = [d for d in dirnames if d[0] not in "._"]
                    for filename in filenames:
                        if os.path.splitext(filename)[1].lower() == ".java":
                            yield os.path.join(dirpath, filename).lstrip("./")


            def md_prefix(i):
                return f"{i * '  '}*" if i else "\n##"


            def print_path(old_path: str, new_path: str) -> str:
                global g_output
                old_parts = old_path.split(os.sep)
                mid_diff = False
                new_parts = new_path.split(os.sep)
                for i, new_part in enumerate(new_parts):
                    if i + 1 > len(old_parts) or old_parts[i] != new_part or mid_diff:
                        if i + 1 < len(new_parts):
                            mid_diff = True
                        if new_part:
                            g_output.append(f"{md_prefix(i)} {new_part.replace('_', ' ')}")
                return new_path


            def build_directory_md(top_dir: str = ".") -> str:
                global g_output
                old_path = ""
                for filepath in sorted(good_filepaths(top_dir), key=str.lower):
                    filepath, filename = os.path.split(filepath)
                    if filepath != old_path:
                        old_path = print_path(old_path, filepath)
                    indent = (filepath.count(os.sep) + 1) if filepath else 0
                    url = "/".join((URL_BASE, filepath, filename)).replace(" ", "%20")
                    filename = os.path.splitext(filename.replace("_", " "))[0]
                    g_output.append(f"{md_prefix(indent)} [{filename}]({url})")
                return "\n".join(g_output)


            with open("DIRECTORY.md", "w") as out_file:
                out_file.write(build_directory_md(".") + "\n")

      - name: Update DIRECTORY.md
        run: |
          cat DIRECTORY.md
          git config --global user.name "$GITHUB_ACTOR"
          git config --global user.email "$<EMAIL>"
          git remote set-url origin https://x-access-token:${{ secrets.GITHUB_TOKEN }}@github.com/$GITHUB_REPOSITORY
          git add DIRECTORY.md
          git commit -am "Update directory" ||  true
          git push --force origin HEAD:$GITHUB_REF || true
