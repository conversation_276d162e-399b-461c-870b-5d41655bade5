---
name: "CodeQL"

on:
  workflow_dispatch:
  push:
    branches:
      - master
  pull_request:
  schedule:
    - cron: '53 3 * * 0'

env:
  LANGUAGE: 'java-kotlin'

jobs:
  analyze:
    name: Analyze
    runs-on: 'ubuntu-latest'
    permissions:
      actions: read
      contents: read
      security-events: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          java-version: 21
          distribution: 'temurin'

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: ${{ env.LANGUAGE }}

      - name: Build
        run: mvn --batch-mode --update-snapshots verify

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
        with:
          category: "/language:${{env.LANGUAGE}}"
...
